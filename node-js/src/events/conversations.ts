import type {
  GetMessagesBody,
  JoinConversationBody,
  ReadMessagesBody,
  SendMessagesBody,
  SocketEventParams,
} from "~/socket";

import { events } from "~/lib/events";
import { prisma } from "~/lib/prisma";

async function handleGetConversations({ io, socket }: SocketEventParams) {
  const userAuth = socket.request.user;

  try {
    console.log("Get Conversations for: ", { user: userAuth });

    const conversations = await prisma.conversation.findMany({
      where: {
        members: {
          some: {
            id: userAuth.id,
          },
        },
      },
    });

    socket.join(userAuth.id);

    io.in(userAuth.id).emit(events.conversations.receive, conversations);

    console.log("Conversations: ", conversations);
  } catch (error) {
    console.error("Error getting conversations:", error);
  }
}

async function handleJoinConversations({
  io,
  socket,
  type,
  memberAuthId,
  referenceId,
}: SocketEventParams & JoinConversationBody) {
  const userAuth = socket.request.user;

  try {
    console.log("Join Conversation for: ", {
      user: userAuth,
      type,
      memberAuthId,
      referenceId,
    });

    let conversation = await prisma.conversation.findFirst({
      where: {
        type,
        referenceId,
        AND: [
          { members: { some: { authId: userAuth.id } } },
          { members: { some: { authId: memberAuthId } } },
        ],
      },
    });

    if (!conversation) {
      console.log("Create Conversation for: ", {
        user: userAuth,
        type,
        memberAuthId,
        referenceId,
      });

      conversation = await prisma.conversation.create({
        data: {
          type,
          referenceId,
        },
      });

      await prisma.conversationToAuth.createMany({
        data: [
          {
            conversationId: conversation.id,
            authId: userAuth.id,
          },
          {
            conversationId: conversation.id,
            authId: memberAuthId,
          },
        ],
      });

      let conversationFor = "";

      switch (type) {
        case "VENDOR":
          conversationFor = "for order";
          break;
        case "LOGISTIC":
          conversationFor = "for delivery";
          break;
      }

      // TODO: Send First Message From User (Not Vendor/Logistic)
    }

    socket.join(conversation.id);

    console.log("Conversation: ", conversation);
  } catch (error) {
    console.error("Error joining conversation:", error);
  }
}

async function handleGetMessages({
  io,
  socket,
  conversationId,
}: SocketEventParams & GetMessagesBody) {
  const userAuth = socket.request.user;

  try {
    console.log("Get Messages for: ", { user: userAuth });

    const messages = await prisma.message.findMany({
      where: {
        conversationId,
      },
    });

    io.in(conversationId).emit(events.messages.receive, messages);

    console.log("Messages: ", messages);
  } catch (error) {
    console.error("Error getting messages:", error);
  }
}

async function handleSendMessages({
  io,
  socket,
  conversationId,
  content,
}: SocketEventParams & SendMessagesBody) {
  const userAuth = socket.request.user;

  try {
    console.log("Send Messages for: ", { user: userAuth });

    const conversation = await prisma.conversation.findUnique({
      where: { id: conversationId },
    });

    if (!conversation) {
      throw new Error("Conversation Not Found!");
    }

    const message = await prisma.message.create({
      data: {
        content,
        sender: {
          connect: { id: userAuth.id },
        },
        conversation: {
          connect: { id: conversation.id },
        },
      },
    });

    console.log("Message: ", message);
  } catch (error) {
    console.error("Error sending message:", error);
  }
}

async function handleReadMessages({
  io,
  socket,
  conversationId,
  messageIds,
}: SocketEventParams & ReadMessagesBody) {
  const userAuth = socket.request.user;

  try {
    console.log("Read Messages for: ", { user: userAuth });

    const messages = await prisma.message.updateMany({
      where: {
        id: { in: messageIds },
        senderId: { not: userAuth.id },
        conversationId,
        isRead: false,
      },
      data: {
        isRead: true,
      },
    });

    console.log("Messages: ", messages);
  } catch (error) {
    console.error("Error reading messages:", error);
  }
}

export {
  handleGetConversations,
  handleJoinConversations,
  handleGetMessages,
  handleSendMessages,
  handleReadMessages,
};
